/* Birthday Panel Component Styles */

/* Fade In Animation */
.fade-in {
  animation: fadeIn 0.5s ease-out;
}

/* Modern Stats Cards */
.modern-stats-card {
  border-radius: var(--border-radius-lg);
  padding: 1.5rem;
  height: 100%;
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  margin-bottom: 1rem;
  color: white;
}

.modern-stats-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.modern-stats-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  margin-right: 1rem;
  background-color: rgba(255, 255, 255, 0.2);
  flex-shrink: 0;
}

.modern-stats-info {
  flex-grow: 1;
}

.modern-stats-value {
  font-size: 1.75rem;
  font-weight: 700;
  margin-bottom: 0.25rem;
}

.modern-stats-label {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.modern-stats-subtext {
  font-size: 0.875rem;
  opacity: 0.8;
}

/* Background Gradients */
.bg-primary-gradient {
  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
}

.bg-success-gradient {
  background: linear-gradient(135deg, var(--success) 0%, #1e7e34 100%);
}

.bg-info-gradient {
  background: linear-gradient(135deg, var(--info) 0%, #0097b2 100%);
}

/* Header Actions */
.header-actions {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

/* Empty State */
.empty-state {
  padding: 3rem 1rem;
  text-align: center;
  color: var(--text-secondary);
}

.empty-icon {
  margin-bottom: 1.5rem;
  color: var(--text-muted);
}

.empty-state h4 {
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.empty-state p {
  color: var(--text-secondary);
  font-size: 1rem;
}

/* Member Info in Table */
.member-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.modern-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 0.875rem;
  flex-shrink: 0;
}

.member-details {
  flex-grow: 1;
}

.member-name {
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.25rem;
}

.member-id {
  font-size: 0.75rem;
  color: var(--text-secondary);
}

/* Contact Info */
.contact-info {
  color: var(--text-primary);
}

.phone-number {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.875rem;
}

/* Birth Date */
.birth-date {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: var(--text-primary);
  font-size: 0.875rem;
}

/* Days Left Classes */
.days-today {
  color: var(--danger) !important;
  font-weight: 700;
  background-color: var(--danger-light);
  padding: 0.25rem 0.5rem;
  border-radius: var(--border-radius-sm);
}

.days-tomorrow {
  color: var(--warning) !important;
  font-weight: 600;
  background-color: var(--warning-light);
  padding: 0.25rem 0.5rem;
  border-radius: var(--border-radius-sm);
}

.days-soon {
  color: var(--info) !important;
  font-weight: 600;
  background-color: var(--info-light);
  padding: 0.25rem 0.5rem;
  border-radius: var(--border-radius-sm);
}

.days-normal {
  color: var(--success) !important;
  font-weight: 500;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: 0.5rem;
  justify-content: center;
}

/* Zoom In Animation for Table Rows */
.zoom-in {
  animation: zoomIn 0.3s ease-out;
}

/* Dialog Styles */
.birthday-panel-container {
  padding: 1.5rem;
  max-width: 600px;
  margin: 0 auto;
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--border-color);
}

.dialog-header h2 {
  margin: 0;
  color: var(--text-primary);
  font-size: 1.5rem;
  font-weight: 600;
}

.dialog-stats {
  display: flex;
  gap: 0.5rem;
}

.stats-badge {
  background-color: var(--primary-light);
  color: var(--primary);
  padding: 0.5rem 1rem;
  border-radius: var(--border-radius-pill);
  font-size: 0.875rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.dialog-content {
  margin-bottom: 1.5rem;
}

.dialog-members-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  max-height: 400px;
  overflow-y: auto;
}

.dialog-member-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background-color: var(--bg-secondary);
  border-radius: var(--border-radius-md);
  border: 1px solid var(--border-color);
  transition: all 0.3s ease;
}

.dialog-member-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

.dialog-member-card .member-details {
  flex-grow: 1;
}

.dialog-member-card .member-name {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.member-birth-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.member-birth-info .birth-date {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.dialog-actions {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  padding-top: 1rem;
  border-top: 1px solid var(--border-color);
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes zoomIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Dark Mode Support */
[data-theme="dark"] .modern-stats-card {
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .modern-stats-card:hover {
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.4);
}

[data-theme="dark"] .dialog-member-card {
  background-color: var(--bg-tertiary);
}

[data-theme="dark"] .dialog-member-card:hover {
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .stats-badge {
  background-color: rgba(67, 97, 238, 0.2);
  color: var(--primary);
}

/* Responsive Design */
@media (max-width: 767.98px) {
  .modern-stats-card {
    margin-bottom: 1rem;
  }

  .modern-stats-icon {
    width: 50px;
    height: 50px;
    font-size: 1.25rem;
  }

  .modern-stats-value {
    font-size: 1.5rem;
  }

  .header-actions {
    margin-top: 1rem;
    width: 100%;
  }

  .dialog-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .dialog-stats {
    width: 100%;
    justify-content: center;
  }

  .dialog-actions {
    flex-direction: column;
    gap: 0.5rem;
  }

  .dialog-actions .modern-btn {
    width: 100%;
  }

  .action-buttons {
    flex-direction: column;
    gap: 0.25rem;
  }

  .action-buttons .modern-btn {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
  }

  .member-birth-info {
    font-size: 0.75rem;
  }

  .birthday-panel-container {
    padding: 1rem;
  }
}

/* Dark mode specific overrides */
[data-theme="dark"] .member-card {
  border: 1px solid var(--border-color, #343a40);
}

[data-theme="dark"] .message-template {
  background-color: var(--bg-tertiary, #2d2d2d);
}

[data-theme="dark"] .member-details h4,
[data-theme="dark"] .member-details p {
  color: var(--text-color, #e9ecef);
}

[data-theme="dark"] .days-left {
  color: var(--accent-color, #4895ef) !important;
}

[data-theme="dark"] .no-birthdays {
  color: var(--text-muted, #adb5bd);
}

/* Fix for textarea placeholder in dark mode */
[data-theme="dark"] textarea::placeholder {
  color: var(--text-muted, #adb5bd);
  opacity: 0.7;
}

[data-theme="dark"] textarea {
  color: var(--text-color, #e9ecef);
  background-color: var(--bg-tertiary, #2d2d2d);
}

@media (max-width: 768px) {
  .member-card {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .member-actions {
    margin-top: 15px;
    width: 100%;
  }
  
  .member-actions button {
    width: 100%;
  }
}
